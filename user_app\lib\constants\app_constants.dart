import 'package:flutter/material.dart';

class AppConstants {
  // App Information
  static const String appName = 'Amal Point';
  static const String appVersion = '1.0.0';

  // Colors
  static const Color primaryColor = Color(0xFF6C63FF);
  static const Color secondaryColor = Color(0xFF03DAC6);
  static const Color backgroundColor = Color(0xFFF8F9FA);
  static const Color surfaceColor = Color(0xFFFFFFFF);
  static const Color errorColor = Color(0xFFE53E3E);
  static const Color successColor = Color(0xFF38A169);
  static const Color warningColor = Color(0xFFD69E2E);
  static const Color onPrimaryColor = Color(0xFFFFFFFF);
  static const Color onSecondaryColor = Color(0xFF000000);
  static const Color onBackgroundColor = Color(0xFF000000);
  static const Color onSurfaceColor = Color(0xFF000000);
  static const Color onErrorColor = Color(0xFFFFFFFF);

  // Navigation Colors
  static const Color navActiveColor = primaryColor;
  static const Color navInactiveColor = Color(0xFF9CA3AF);
  static const Color navBackgroundColor = Color(0xFFFFFFFF);
  static const Color navShadowColor = Color(0x1A000000);

  // Gradient Colors
  static const List<Color> primaryGradient = [
    Color(0xFF6C63FF),
    Color(0xFF5A52E8),
  ];
  static const List<Color> secondaryGradient = [
    Color(0xFF03DAC6),
    Color(0xFF00BFA5),
  ];
  static const List<Color> backgroundGradient = [
    Color(0xFFF8F9FA),
    Color(0xFFE9ECEF),
  ];

  // Text Colors
  static const Color textPrimaryColor = Color(0xFF212121);
  static const Color textSecondaryColor = Color(0xFF757575);
  static const Color textHintColor = Color(0xFFBDBDBD);
  static const Color textColor = Color(0xFF212121); // Alias for textPrimaryColor

  // Border Colors
  static const Color borderColor = Color(0xFFE0E0E0);

  // Spacing
  static const double paddingXSmall = 4.0;
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;
  static const double paddingExtraLarge = 40.0;

  // Border Radius
  static const double borderRadiusSmall = 4.0;
  static const double borderRadiusMedium = 8.0;
  static const double borderRadiusLarge = 12.0;
  static const double borderRadiusXLarge = 16.0;

  // Font Sizes
  static const double fontSizeXSmall = 10.0;
  static const double fontSizeSmall = 12.0;
  static const double fontSizeMedium = 14.0;
  static const double fontSizeLarge = 16.0;
  static const double fontSizeXLarge = 18.0;
  static const double fontSizeXXLarge = 20.0;
  static const double fontSizeTitle = 24.0;
  static const double fontSizeHeading = 28.0;

  // Icon Sizes
  static const double iconSizeSmall = 16.0;
  static const double iconSizeMedium = 24.0;
  static const double iconSizeLarge = 32.0;
  static const double iconSizeXLarge = 48.0;

  // Animation Durations
  static const Duration animationDurationShort = Duration(milliseconds: 200);
  static const Duration animationDurationMedium = Duration(milliseconds: 300);
  static const Duration animationDurationLong = Duration(milliseconds: 500);

  // API Configuration
  static const String baseUrl = 'https://your-api-base-url.com';
  static const Duration apiTimeout = Duration(seconds: 30);

  // Cloudinary Configuration (You'll need to replace with your actual values)
  static const String cloudinaryCloudName = 'your_cloud_name';
  static const String cloudinaryApiKey = 'your_api_key';
  static const String cloudinaryApiSecret = 'your_api_secret';
  static const String cloudinaryUploadPreset = 'your_upload_preset';

  // Firebase Collections
  static const String usersCollection = 'users';
  static const String postsCollection = 'posts';
  static const String productsCollection = 'products';
  static const String chatsCollection = 'chats';
  static const String messagesCollection = 'messages';

  // Image Configuration
  static const int maxImageSizeBytes = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageExtensions = ['jpg', 'jpeg', 'png', 'gif'];

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 50;

  // Validation
  static const int minPasswordLength = 6;
  static const int maxUsernameLength = 30;
  static const int maxBioLength = 150;
  static const int maxPostContentLength = 500;

  // Bottom Navigation
  static const List<String> bottomNavLabels = [
    'Feed',
    'Search',
    'Market',
    'Chat',
    'Profile'
  ];

  static const List<IconData> bottomNavIcons = [
    Icons.home,
    Icons.search,
    Icons.shopping_bag,
    Icons.chat,
    Icons.person
  ];
}
